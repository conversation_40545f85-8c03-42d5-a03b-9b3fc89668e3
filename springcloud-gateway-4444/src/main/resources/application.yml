server:
  port: 4444

spring:
  application:
    name: gateway-service
  main:
    allow-bean-definition-overriding: true

#  redis:
#    host: localhost
#    port: 6379

  cloud:
    nacos:
      server-addr: 127.0.0.1:8848
    gateway:
      #      routes:
      #        - id: orderRouter
      #          uri: http://localhost:8888
      #          predicates:
      #            - Path=/api/orders/**     # Path断言：当请求的地址是以：/api/order开头，则将这次请求交给http://localhost:8888/
      #        - id: goodsRouter
      #          uri: http://localhost:9999  # 只能调用9999端口的微服务。没有办法试下负载均衡
      #          predicates:
      #            - Path=/api/goods/**     # Path断言：当请求的地址是以：/api/goods开头，则将这次请求交给http://localhost:9999/
      routes:
        - id: orderRouter
          uri: lb://order-service
          predicates:
            - Path=/api/orders/**     # Path断言：当请求的地址是以：/api/order开头，则将这次请求交给http://localhost:8888/
        - id: goodsRouter
          uri: lb://goods-service  # 只能调用9999端口的微服务。没有办法试下负载均衡
          predicates:
            - Path=/api/goods/**     # Path断言：当请求的地址是以：/api/goods开头，则将这次请求交给http://localhost:9999/
          #            - Between=2025-06-28T15:22:22+08:00[Asia/Shanghai], 2025-06-28T15:24:30+08:00[Asia/Shanghai]
          #            - Parameter=lisi
          filters:
          #            - StripPrefix=1    # http://localhost:9999/api/goods/------>http://localhost:9999/goods/ 将URL路径/afesfds/api/goods/的第一个前缀路径删掉
        #            - AddRequestHeader=X-Request-color, red
        #            - CheckAuth=username, zhangsan
        - id: secondKillRouter
          uri: lb://secondKill-service
          predicates:
            - Path=/api/secondKill/**
          filters:
            - name: RequestRateLimiter #限流过滤器
            - args:
                - redis-rate-limiter.replenishRate: 1 #令牌桶每秒填充速率
                - redis-rate-limiter.burstCapacity: 2 #令牌桶的总容量
                - key-resolver: "#{@keyResolver}" #使用SpEL表达式，从Spring容器中获取Bean对象

      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
