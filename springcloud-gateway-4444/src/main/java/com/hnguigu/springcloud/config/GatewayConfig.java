package com.hnguigu.springcloud.config;

import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import reactor.core.publisher.Mono;

/**
 * Gateway 跨域配置
 * 注意：此配置与 application.yml 中的 globalcors 配置二选一使用
 * 如果启用此配置，需要注释掉 application.yml 中的 globalcors 配置
 */
@Configuration
public class GatewayConfig {

    /**
     * 编程式全局 CORS 配置
     * 相比配置文件方式，这种方式可以实现更复杂的跨域逻辑
     */
    @Bean
    public CorsWebFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();

        // 是否允许携带认证信息（如 Cookie、Authorization header）
        config.setAllowCredentials(true);

        // 允许的 HTTP 方法
        config.addAllowedMethod("GET");
        config.addAllowedMethod("POST");
        config.addAllowedMethod("PUT");
        config.addAllowedMethod("DELETE");
        config.addAllowedMethod("OPTIONS");
        // 或者使用 config.addAllowedMethod("*"); 允许所有方法

        // 允许的域名（生产环境建议指定具体域名）
        config.addAllowedOrigin("*"); // 推荐使用 addAllowedOriginPattern 而不是 addAllowedOrigin
        // 生产环境示例：
        // config.addAllowedOrigin("http://localhost:3000");
        // config.addAllowedOrigin("https://yourdomain.com");

        // 允许的请求头
        config.addAllowedHeader("*");
        // 或者指定具体的请求头：
        // config.addAllowedHeader("Content-Type");
        // config.addAllowedHeader("Authorization");
        // config.addAllowedHeader("X-Requested-With");

        // 预检请求的缓存时间（秒）
        config.setMaxAge(3600L);

        // 暴露给客户端的响应头
        config.addExposedHeader("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        return new CorsWebFilter(source);
    }

    @Bean
    public KeyResolver keyResolver() {
        return exchange -> Mono.just(exchange.getRequest().getURI().getPath());
    }
}
